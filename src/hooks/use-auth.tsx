import { useEffect, useState } from 'react'
import axios from 'axios'
import { getCookie, setCookie, removeCookie } from 'typescript-cookie'

const API_BASE_URL = 'http://localhost:8000/api'

export const useAuth = () => {
    const [token, setToken] = useState<string | null>(null)
    const [error, setError] = useState<string>('')
    const [loading, setLoading] = useState<boolean>(false)
    const [isAuth, setIsAuth] = useState<boolean>(false)

    useEffect(() => {

        const savedToken = getCookie('token')
        if (savedToken) {
            setToken(savedToken)
            setIsAuth(true)
        }
    }, [])

    const login = async (email: string, password: string) => {
        setLoading(true)
        setError('')
        try {
            const res = await axios.post(`${API_BASE_URL}/auth/login`, { email, password })
            const accessToken = res.data.token

            setToken(accessToken)
            setIsAuth(true)
            setCookie('token', 'accessToken')
        } catch (err: any) {
            setError(err.response?.data?.message || 'Login failed')
            setIsAuth(false)
        } finally {
            setLoading(false)
        }
    }

    const logout = () => {
        setToken(null)
        setIsAuth(false)
        removeCookie('token')
    }

    return {
        token,
        isAuth,
        error,
        loading,
        login,
        logout,
    }
}
