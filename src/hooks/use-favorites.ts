"use client"

import { useState, useEffect, useCallback } from "react"

const FAVORITES_KEY = "work-site-favorites"

export function useFavorites() {
  const [favorites, setFavorites] = useState<number[]>([])
  const [isLoaded, setIsLoaded] = useState(false)

  // Load favorites from localStorage on mount
  useEffect(() => {
    const loadFavorites = () => {
      if (typeof window !== "undefined") {
        try {
          const stored = localStorage.getItem(FAVORITES_KEY)
          if (stored && stored !== "null" && stored !== "undefined") {
            const parsedFavorites = JSON.parse(stored)
            if (Array.isArray(parsedFavorites)) {
              setFavorites(parsedFavorites)
            }
          } else {
            // Initialize with empty array if no data exists
            setFavorites([])
          }
        } catch (error) {
          console.error("Error loading favorites from localStorage:", error)
          setFavorites([])
        }
        setIsLoaded(true)
      }
    }

    // Add a small delay to ensure DOM is ready
    const timer = setTimeout(loadFavorites, 100)
    return () => clearTimeout(timer)
  }, [])

  // Listen for storage changes from other tabs/windows
  useEffect(() => {
    if (typeof window !== "undefined") {
      const handleStorageChange = (e: StorageEvent) => {
        if (e.key === FAVORITES_KEY && e.newValue) {
          try {
            const newFavorites = JSON.parse(e.newValue)
            if (Array.isArray(newFavorites)) {
              setFavorites(newFavorites)
            }
          } catch (error) {
            console.error("Error parsing storage event:", error)
          }
        }
      }

      window.addEventListener('storage', handleStorageChange)
      return () => window.removeEventListener('storage', handleStorageChange)
    }
  }, [])

  // Save favorites to localStorage whenever favorites change (but only after initial load)
  useEffect(() => {
    if (typeof window !== "undefined" && isLoaded) {
      try {
        localStorage.setItem(FAVORITES_KEY, JSON.stringify(favorites))
        // Force a storage event for other tabs/windows
        window.dispatchEvent(new StorageEvent('storage', {
          key: FAVORITES_KEY,
          newValue: JSON.stringify(favorites),
          storageArea: localStorage
        }))
      } catch (error) {
        console.error("Error saving favorites to localStorage:", error)
      }
    }
  }, [favorites, isLoaded])

  const toggleFavorite = useCallback((id: number) => {
    setFavorites((prev) => {
      const newFavorites = prev.includes(id)
        ? prev.filter((fav) => fav !== id)
        : [...prev, id]
      return newFavorites
    })
  }, [])

  const isFavorite = useCallback((id: number) => favorites.includes(id), [favorites])

  const addFavorite = useCallback((id: number) => {
    setFavorites((prev) => {
      if (!prev.includes(id)) {
        return [...prev, id]
      }
      return prev
    })
  }, [])

  const removeFavorite = useCallback((id: number) => {
    setFavorites((prev) => prev.filter((fav) => fav !== id))
  }, [])

  const clearFavorites = useCallback(() => {
    setFavorites([])
  }, [])

  return {
    favorites,
    toggleFavorite,
    isFavorite,
    addFavorite,
    removeFavorite,
    clearFavorites,
    isLoaded,
  }
}
