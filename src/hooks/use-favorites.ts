"use client"

import { useState, useEffect } from "react"

const FAVORITES_KEY = "work-site-favorites"

export function useFavorites() {
  const [favorites, setFavorites] = useState<number[]>([])

  // Load favorites from localStorage on mount
  useEffect(() => {
    if (typeof window !== "undefined") {
      try {
        const stored = localStorage.getItem(FAVORITES_KEY)
        if (stored) {
          setFavorites(JSON.parse(stored))
        }
      } catch (error) {
        console.error("Error loading favorites from localStorage:", error)
      }
    }
  }, [])

  // Save favorites to localStorage whenever favorites change
  useEffect(() => {
    if (typeof window !== "undefined") {
      try {
        localStorage.setItem(FAVORITES_KEY, JSON.stringify(favorites))
      } catch (error) {
        console.error("Error saving favorites to localStorage:", error)
      }
    }
  }, [favorites])

  const toggleFavorite = (id: number) => {
    setFavorites((prev) => {
      if (prev.includes(id)) {
        return prev.filter((fav) => fav !== id)
      } else {
        return [...prev, id]
      }
    })
  }

  const isFavorite = (id: number) => favorites.includes(id)

  const addFavorite = (id: number) => {
    if (!favorites.includes(id)) {
      setFavorites((prev) => [...prev, id])
    }
  }

  const removeFavorite = (id: number) => {
    setFavorites((prev) => prev.filter((fav) => fav !== id))
  }

  const clearFavorites = () => {
    setFavorites([])
  }

  return {
    favorites,
    toggleFavorite,
    isFavorite,
    addFavorite,
    removeFavorite,
    clearFavorites,
  }
}
