"use client"

import * as React from "react"
import { useRouter, useParams } from "next/navigation"
import { IconArrowLeft, IconDeviceFloppy } from "@tabler/icons-react"

import { WorkSite } from "@/types/work-site"
import { AppSidebar } from "@/components/app-sidebar"
import { SiteHeader } from "@/components/site-header"
import {
  SidebarInset,
  SidebarProvider,
} from "@/components/ui/sidebar"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Skeleton } from "@/components/ui/skeleton"

// Import mock data
import workSitesData from "@/mock/work-sites.json"

export default function EditWorkSitePage() {
  const router = useRouter()
  const params = useParams()
  const workSiteId = parseInt(params.id as string)
  
  const [isLoading, setIsLoading] = React.useState(false)
  const [isLoadingData, setIsLoadingData] = React.useState(true)
  const [workSite, setWorkSite] = React.useState<WorkSite | null>(null)
  const [formData, setFormData] = React.useState<Partial<WorkSite>>({})

  // Load work site data
  React.useEffect(() => {
    const loadWorkSite = async () => {
      try {
        // In a real app, you would fetch from API
        // For now, we'll use mock data
        const foundWorkSite = (workSitesData as WorkSite[]).find(
          (site) => site.id === workSiteId
        )
        
        if (foundWorkSite) {
          setWorkSite(foundWorkSite)
          setFormData(foundWorkSite)
        } else {
          // Work site not found, redirect to list
          router.push("/work-sites")
        }
      } catch (error) {
        console.error("Error loading work site:", error)
        router.push("/work-sites")
      } finally {
        setIsLoadingData(false)
      }
    }

    if (workSiteId) {
      loadWorkSite()
    }
  }, [workSiteId, router])

  const updateFormData = (field: keyof WorkSite, value: any) => {
    setFormData((prev) => ({ ...prev, [field]: value }))
  }

  const updateNestedFormData = (
    parentField: keyof WorkSite,
    field: string,
    value: any
  ) => {
    setFormData((prev) => ({
      ...prev,
      [parentField]: {
        ...(prev[parentField] as any),
        [field]: value,
      },
    }))
  }

  const handleSave = async () => {
    setIsLoading(true)
    
    try {
      // Validate required fields
      if (!formData.name || !formData.location || !formData.manager) {
        alert("กรุณากรอกข้อมูลที่จำเป็น")
        return
      }

      // Create updated work site object
      const updatedWorkSite: WorkSite = {
        ...workSite!,
        ...formData,
      } as WorkSite

      // Here you would typically save to your backend
      // For now, we'll just simulate saving and redirect
      console.log("Updating work site:", updatedWorkSite)
      
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000))
      
      // Redirect back to work sites list
      router.push("/work-sites")
    } catch (error) {
      console.error("Error updating work site:", error)
      alert("เกิดข้อผิดพลาดในการบันทึกข้อมูล")
    } finally {
      setIsLoading(false)
    }
  }

  if (isLoadingData) {
    return (
      <SidebarProvider
        style={
          {
            "--sidebar-width": "calc(var(--spacing) * 72)",
            "--header-height": "calc(var(--spacing) * 12)",
          } as React.CSSProperties
        }
      >
        <AppSidebar variant="inset" />
        <SidebarInset>
          <SiteHeader />
          <div className="flex flex-1 flex-col">
            <div className="@container/main flex flex-1 flex-col gap-2">
              <div className="flex flex-col gap-4 py-4 md:gap-6 md:py-6">
                <div className="px-4 lg:px-6">
                  <div className="flex items-center gap-4">
                    <Skeleton className="h-9 w-16" />
                    <div>
                      <Skeleton className="h-8 w-48 mb-2" />
                      <Skeleton className="h-4 w-64" />
                    </div>
                  </div>
                </div>
                <div className="px-4 lg:px-6">
                  <div className="max-w-4xl space-y-6">
                    <Card>
                      <CardHeader>
                        <Skeleton className="h-6 w-32" />
                        <Skeleton className="h-4 w-48" />
                      </CardHeader>
                      <CardContent className="space-y-4">
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                          <Skeleton className="h-10 w-full" />
                          <Skeleton className="h-10 w-full" />
                        </div>
                      </CardContent>
                    </Card>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </SidebarInset>
      </SidebarProvider>
    )
  }

  if (!workSite) {
    return null
  }

  return (
    <SidebarProvider
      style={
        {
          "--sidebar-width": "calc(var(--spacing) * 72)",
          "--header-height": "calc(var(--spacing) * 12)",
        } as React.CSSProperties
      }
    >
      <AppSidebar variant="inset" />
      <SidebarInset>
        <SiteHeader />
        <div className="flex flex-1 flex-col">
          <div className="@container/main flex flex-1 flex-col gap-2">
            <div className="flex flex-col gap-4 py-4 md:gap-6 md:py-6">
              {/* Header */}
              <div className="px-4 lg:px-6">
                <div className="flex items-center gap-4">
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => router.back()}
                  >
                    <IconArrowLeft className="h-4 w-4" />
                    กลับ
                  </Button>
                  <div>
                    <h1 className="text-2xl font-bold tracking-tight">แก้ไขไซต์งาน</h1>
                    <p className="text-muted-foreground">
                      แก้ไขข้อมูลของ {workSite.name}
                    </p>
                  </div>
                </div>
              </div>

              {/* Form */}
              <div className="px-4 lg:px-6">
                <div className="max-w-4xl space-y-6">
                  {/* Basic Information */}
                  <Card>
                    <CardHeader>
                      <CardTitle>ข้อมูลพื้นฐาน</CardTitle>
                      <CardDescription>
                        ข้อมูลหลักของไซต์งาน
                      </CardDescription>
                    </CardHeader>
                    <CardContent className="space-y-4">
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div className="space-y-2">
                          <Label htmlFor="name">
                            ชื่อไซต์งาน <Badge variant="destructive" className="ml-1 text-xs">*</Badge>
                          </Label>
                          <Input
                            id="name"
                            value={formData.name || ""}
                            onChange={(e) => updateFormData("name", e.target.value)}
                            placeholder="ระบุชื่อไซต์งาน"
                          />
                        </div>
                        <div className="space-y-2">
                          <Label htmlFor="location">
                            สถานที่ <Badge variant="destructive" className="ml-1 text-xs">*</Badge>
                          </Label>
                          <Input
                            id="location"
                            value={formData.location || ""}
                            onChange={(e) => updateFormData("location", e.target.value)}
                            placeholder="จังหวัด/เมือง"
                          />
                        </div>
                      </div>

                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div className="space-y-2">
                          <Label htmlFor="status">สถานะ</Label>
                          <Select
                            value={formData.status || ""}
                            onValueChange={(value) => updateFormData("status", value)}
                          >
                            <SelectTrigger>
                              <SelectValue placeholder="เลือกสถานะ" />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value="Active">ใช้งาน</SelectItem>
                              <SelectItem value="Inactive">ไม่ใช้งาน</SelectItem>
                              <SelectItem value="Maintenance">ซ่อมบำรุง</SelectItem>
                              <SelectItem value="Completed">เสร็จสิ้น</SelectItem>
                            </SelectContent>
                          </Select>
                        </div>
                        <div className="space-y-2">
                          <Label htmlFor="type">ประเภท</Label>
                          <Select
                            value={formData.type || ""}
                            onValueChange={(value) => updateFormData("type", value)}
                          >
                            <SelectTrigger>
                              <SelectValue placeholder="เลือกประเภท" />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value="Construction">ก่อสร้าง</SelectItem>
                              <SelectItem value="Manufacturing">โรงงาน</SelectItem>
                              <SelectItem value="Office">สำนักงาน</SelectItem>
                              <SelectItem value="Warehouse">คลังสินค้า</SelectItem>
                              <SelectItem value="Retail">ร้านค้า</SelectItem>
                            </SelectContent>
                          </Select>
                        </div>
                      </div>

                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div className="space-y-2">
                          <Label htmlFor="manager">
                            ผู้จัดการ <Badge variant="destructive" className="ml-1 text-xs">*</Badge>
                          </Label>
                          <Input
                            id="manager"
                            value={formData.manager || ""}
                            onChange={(e) => updateFormData("manager", e.target.value)}
                            placeholder="ชื่อผู้จัดการ"
                          />
                        </div>
                        <div className="space-y-2">
                          <Label htmlFor="employees">จำนวนพนักงาน</Label>
                          <Input
                            id="employees"
                            type="number"
                            min="0"
                            value={formData.employees || ""}
                            onChange={(e) => updateFormData("employees", parseInt(e.target.value) || 0)}
                            placeholder="0"
                          />
                        </div>
                      </div>

                      <div className="space-y-2">
                        <Label htmlFor="description">รายละเอียด</Label>
                        <Textarea
                          id="description"
                          value={formData.description || ""}
                          onChange={(e) => updateFormData("description", e.target.value)}
                          placeholder="รายละเอียดเพิ่มเติมเกี่ยวกับไซต์งาน"
                          rows={3}
                        />
                      </div>
                    </CardContent>
                  </Card>

                  {/* Project Details */}
                  <Card>
                    <CardHeader>
                      <CardTitle>รายละเอียดโครงการ</CardTitle>
                      <CardDescription>
                        ข้อมูลเกี่ยวกับงบประมาณและระยะเวลา
                      </CardDescription>
                    </CardHeader>
                    <CardContent className="space-y-4">
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div className="space-y-2">
                          <Label htmlFor="budget">งบประมาณ (บาท)</Label>
                          <Input
                            id="budget"
                            type="number"
                            min="0"
                            value={formData.budget || ""}
                            onChange={(e) => updateFormData("budget", parseInt(e.target.value) || 0)}
                            placeholder="0"
                          />
                        </div>
                        <div className="space-y-2">
                          <Label htmlFor="progress">ความคืบหน้า (%)</Label>
                          <Input
                            id="progress"
                            type="number"
                            min="0"
                            max="100"
                            value={formData.progress || ""}
                            onChange={(e) => updateFormData("progress", parseInt(e.target.value) || 0)}
                            placeholder="0"
                          />
                        </div>
                      </div>

                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div className="space-y-2">
                          <Label htmlFor="startDate">วันที่เริ่มต้น</Label>
                          <Input
                            id="startDate"
                            type="date"
                            value={formData.startDate || ""}
                            onChange={(e) => updateFormData("startDate", e.target.value)}
                          />
                        </div>
                        <div className="space-y-2">
                          <Label htmlFor="endDate">วันที่สิ้นสุด</Label>
                          <Input
                            id="endDate"
                            type="date"
                            value={formData.endDate || ""}
                            onChange={(e) => updateFormData("endDate", e.target.value)}
                          />
                        </div>
                      </div>
                    </CardContent>
                  </Card>

                  {/* Contact Information */}
                  <Card>
                    <CardHeader>
                      <CardTitle>ข้อมูลติดต่อ</CardTitle>
                      <CardDescription>
                        ข้อมูลการติดต่อและที่อยู่
                      </CardDescription>
                    </CardHeader>
                    <CardContent className="space-y-4">
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div className="space-y-2">
                          <Label htmlFor="phone">เบอร์โทรศัพท์</Label>
                          <Input
                            id="phone"
                            value={formData.contact?.phone || ""}
                            onChange={(e) => updateNestedFormData("contact", "phone", e.target.value)}
                            placeholder="0xx-xxx-xxxx"
                          />
                        </div>
                        <div className="space-y-2">
                          <Label htmlFor="email">อีเมล</Label>
                          <Input
                            id="email"
                            type="email"
                            value={formData.contact?.email || ""}
                            onChange={(e) => updateNestedFormData("contact", "email", e.target.value)}
                            placeholder="<EMAIL>"
                          />
                        </div>
                      </div>

                      <div className="space-y-2">
                        <Label htmlFor="street">ที่อยู่</Label>
                        <Input
                          id="street"
                          value={formData.address?.street || ""}
                          onChange={(e) => updateNestedFormData("address", "street", e.target.value)}
                          placeholder="เลขที่ ถนน"
                        />
                      </div>

                      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                        <div className="space-y-2">
                          <Label htmlFor="city">เมือง/อำเภอ</Label>
                          <Input
                            id="city"
                            value={formData.address?.city || ""}
                            onChange={(e) => updateNestedFormData("address", "city", e.target.value)}
                            placeholder="อำเภอ"
                          />
                        </div>
                        <div className="space-y-2">
                          <Label htmlFor="province">จังหวัด</Label>
                          <Input
                            id="province"
                            value={formData.address?.province || ""}
                            onChange={(e) => updateNestedFormData("address", "province", e.target.value)}
                            placeholder="จังหวัด"
                          />
                        </div>
                        <div className="space-y-2">
                          <Label htmlFor="postalCode">รหัสไปรษณีย์</Label>
                          <Input
                            id="postalCode"
                            value={formData.address?.postalCode || ""}
                            onChange={(e) => updateNestedFormData("address", "postalCode", e.target.value)}
                            placeholder="xxxxx"
                          />
                        </div>
                      </div>
                    </CardContent>
                  </Card>

                  {/* Action Buttons */}
                  <div className="flex justify-end gap-4 pb-6">
                    <Button
                      variant="outline"
                      onClick={() => router.back()}
                      disabled={isLoading}
                    >
                      ยกเลิก
                    </Button>
                    <Button
                      onClick={handleSave}
                      disabled={isLoading}
                    >
                      {isLoading ? (
                        "กำลังบันทึก..."
                      ) : (
                        <>
                          <IconDeviceFloppy className="h-4 w-4 mr-2" />
                          บันทึก
                        </>
                      )}
                    </Button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </SidebarInset>
    </SidebarProvider>
  )
}
