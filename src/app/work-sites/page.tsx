"use client"

import * as React from "react"
import { IconPlus, IconHeart, IconGrid3x3, IconList } from "@tabler/icons-react"

import { WorkSite, WorkSiteFilters } from "@/types/work-site"
import { AppSidebar } from "@/components/app-sidebar"
import { SiteHeader } from "@/components/site-header"
import { WorkSiteCard } from "@/components/work-site-card"
import { WorkSiteFilters as WorkSiteFiltersComponent } from "@/components/work-site-filters"
import { WorkSiteEditDialog } from "@/components/work-site-edit-dialog"
import {
  SidebarInset,
  SidebarProvider,
} from "@/components/ui/sidebar"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { ToggleGroup, ToggleGroupItem } from "@/components/ui/toggle-group"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"

// Import mock data
import workSitesData from "@/mock/work-sites.json"

export default function WorkSitesPage() {
  const [workSites, setWorkSites] = React.useState<WorkSite[]>(workSitesData as WorkSite[])
  const [searchQuery, setSearchQuery] = React.useState("")
  const [filters, setFilters] = React.useState<WorkSiteFilters>({})
  const [viewMode, setViewMode] = React.useState<"grid" | "list">("grid")
  const [editingWorkSite, setEditingWorkSite] = React.useState<WorkSite | null>(null)
  const [showFavoritesOnly, setShowFavoritesOnly] = React.useState(false)

  // Filter and search logic
  const filteredWorkSites = React.useMemo(() => {
    let filtered = workSites

    // Search filter
    if (searchQuery) {
      const query = searchQuery.toLowerCase()
      filtered = filtered.filter(
        (site) =>
          site.name.toLowerCase().includes(query) ||
          site.location.toLowerCase().includes(query) ||
          site.manager.toLowerCase().includes(query) ||
          site.description.toLowerCase().includes(query)
      )
    }

    // Status filter
    if (filters.status?.length) {
      filtered = filtered.filter((site) => filters.status!.includes(site.status))
    }

    // Type filter
    if (filters.type?.length) {
      filtered = filtered.filter((site) => filters.type!.includes(site.type))
    }

    // Manager filter
    if (filters.manager) {
      filtered = filtered.filter((site) =>
        site.manager.toLowerCase().includes(filters.manager!.toLowerCase())
      )
    }

    // Location filter
    if (filters.location) {
      filtered = filtered.filter((site) =>
        site.location.toLowerCase().includes(filters.location!.toLowerCase())
      )
    }

    // Date range filter
    if (filters.dateRange?.start || filters.dateRange?.end) {
      filtered = filtered.filter((site) => {
        const siteStartDate = new Date(site.startDate)
        const filterStartDate = filters.dateRange?.start
          ? new Date(filters.dateRange.start)
          : null
        const filterEndDate = filters.dateRange?.end
          ? new Date(filters.dateRange.end)
          : null

        if (filterStartDate && siteStartDate < filterStartDate) return false
        if (filterEndDate && siteStartDate > filterEndDate) return false
        return true
      })
    }

    // Favorites filter
    if (showFavoritesOnly) {
      filtered = filtered.filter((site) => site.isFavorite)
    }

    return filtered
  }, [workSites, searchQuery, filters, showFavoritesOnly])

  const handleFavoriteToggle = (id: number) => {
    setWorkSites((prev) =>
      prev.map((site) =>
        site.id === id ? { ...site, isFavorite: !site.isFavorite } : site
      )
    )
  }

  const handleEdit = (workSite: WorkSite) => {
    setEditingWorkSite(workSite)
  }

  const handleSave = (updatedWorkSite: WorkSite) => {
    setWorkSites((prev) =>
      prev.map((site) =>
        site.id === updatedWorkSite.id ? updatedWorkSite : site
      )
    )
    setEditingWorkSite(null)
  }

  const handleClearFilters = () => {
    setFilters({})
    setSearchQuery("")
    setShowFavoritesOnly(false)
  }

  const favoriteCount = workSites.filter((site) => site.isFavorite).length

  return (
    <SidebarProvider
      style={
        {
          "--sidebar-width": "calc(var(--spacing) * 72)",
          "--header-height": "calc(var(--spacing) * 12)",
        } as React.CSSProperties
      }
    >
      <AppSidebar variant="inset" />
      <SidebarInset>
        <SiteHeader />
        <div className="flex flex-1 flex-col">
          <div className="@container/main flex flex-1 flex-col gap-2">
            <div className="flex flex-col gap-4 py-4 md:gap-6 md:py-6">
              {/* Header */}
              <div className="px-4 lg:px-6">
                <div className="flex items-center justify-between">
                  <div>
                    <h1 className="text-2xl font-bold tracking-tight">ไซต์งาน</h1>
                    <p className="text-muted-foreground">
                      จัดการและติดตามไซต์งานทั้งหมด
                    </p>
                  </div>
                  <Button>
                    <IconPlus className="h-4 w-4" />
                    เพิ่มไซต์งานใหม่
                  </Button>
                </div>
              </div>

              {/* Filters and Controls */}
              <div className="px-4 lg:px-6">
                <div className="flex flex-col gap-4">
                  <WorkSiteFiltersComponent
                    searchQuery={searchQuery}
                    onSearchChange={setSearchQuery}
                    filters={filters}
                    onFiltersChange={setFilters}
                    onClearFilters={handleClearFilters}
                  />

                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-4">
                      <Tabs value={showFavoritesOnly ? "favorites" : "all"}>
                        <TabsList>
                          <TabsTrigger
                            value="all"
                            onClick={() => setShowFavoritesOnly(false)}
                          >
                            ทั้งหมด ({workSites.length})
                          </TabsTrigger>
                          <TabsTrigger
                            value="favorites"
                            onClick={() => setShowFavoritesOnly(true)}
                          >
                            <IconHeart className="h-3 w-3 mr-1" />
                            รายการโปรด ({favoriteCount})
                          </TabsTrigger>
                        </TabsList>
                      </Tabs>

                      <Badge variant="secondary">
                        {filteredWorkSites.length} รายการ
                      </Badge>
                    </div>

                    <ToggleGroup
                      type="single"
                      value={viewMode}
                      onValueChange={(value) => value && setViewMode(value as "grid" | "list")}
                    >
                      <ToggleGroupItem value="grid" aria-label="Grid view">
                        <IconGrid3x3 className="h-4 w-4" />
                      </ToggleGroupItem>
                      <ToggleGroupItem value="list" aria-label="List view">
                        <IconList className="h-4 w-4" />
                      </ToggleGroupItem>
                    </ToggleGroup>
                  </div>
                </div>
              </div>

              {/* Work Sites Grid/List */}
              <div className="px-4 lg:px-6">
                {filteredWorkSites.length === 0 ? (
                  <div className="flex flex-col items-center justify-center py-12 text-center">
                    <div className="text-muted-foreground">
                      {searchQuery || Object.keys(filters).length > 0 || showFavoritesOnly
                        ? "ไม่พบไซต์งานที่ตรงกับเงื่อนไขการค้นหา"
                        : "ยังไม่มีไซต์งาน"}
                    </div>
                    {(searchQuery || Object.keys(filters).length > 0 || showFavoritesOnly) && (
                      <Button
                        variant="outline"
                        onClick={handleClearFilters}
                        className="mt-4"
                      >
                        ล้างตัวกรอง
                      </Button>
                    )}
                  </div>
                ) : (
                  <div
                    className={
                      viewMode === "grid"
                        ? "grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4"
                        : "space-y-4"
                    }
                  >
                    {filteredWorkSites.map((workSite) => (
                      <WorkSiteCard
                        key={workSite.id}
                        workSite={workSite}
                        onFavoriteToggle={handleFavoriteToggle}
                        onEdit={handleEdit}
                      />
                    ))}
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      </SidebarInset>

      {/* Edit Dialog */}
      <WorkSiteEditDialog
        workSite={editingWorkSite}
        open={!!editingWorkSite}
        onOpenChange={(open) => !open && setEditingWorkSite(null)}
        onSave={handleSave}
      />
    </SidebarProvider>
  )
}
