export interface WorkSite {
  id: number
  name: string
  location: string
  status: 'Active' | 'Inactive' | 'Maintenance' | 'Completed'
  type: 'Construction' | 'Manufacturing' | 'Office' | 'Warehouse' | 'Retail'
  manager: string
  employees: number
  startDate: string
  endDate?: string
  budget: number
  progress: number
  description: string
  isFavorite: boolean
  image?: string
  tags: string[]
  contact: {
    phone: string
    email: string
  }
  address: {
    street: string
    city: string
    province: string
    postalCode: string
  }
}

export interface WorkSiteFilters {
  status?: string[]
  type?: string[]
  manager?: string
  location?: string
  dateRange?: {
    start: string
    end: string
  }
}