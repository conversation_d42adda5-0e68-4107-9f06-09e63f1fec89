"use client"

import * as React from "react"
import {
    IconFilter,
    IconSearch,
    IconX,
    IconCalendar,
} from "@tabler/icons-react"

import { WorkSiteFilters } from "@/types/work-site"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import {
    Select,
    SelectContent,
    SelectItem,
    SelectTrigger,
    SelectValue,
} from "@/components/ui/select"
import {
    Sheet,
    SheetContent,
    SheetDescription,
    SheetHeader,
    SheetTitle,
    SheetTrigger,
} from "@/components/ui/sheet"
import { Badge } from "@/components/ui/badge"
import { Checkbox } from "@/components/ui/checkbox"
import { Label } from "@/components/ui/label"

interface WorkSiteFiltersProps {
    searchQuery: string
    onSearchChange: (query: string) => void
    filters: WorkSiteFilters
    onFiltersChange: (filters: WorkSiteFilters) => void
    onClearFilters: () => void
}

const statusOptions = [
    { value: "Active", label: "ใช้งาน" },
    { value: "Inactive", label: "ไม่ใช้งาน" },
    { value: "Maintenance", label: "ซ่อมบำรุง" },
    { value: "Completed", label: "เสร็จสิ้น" },
]

const typeOptions = [
    { value: "Construction", label: "ก่อสร้าง" },
    { value: "Manufacturing", label: "โรงงาน" },
    { value: "Office", label: "สำนักงาน" },
    { value: "Warehouse", label: "คลังสินค้า" },
    { value: "Retail", label: "ร้านค้า" },
]

export function WorkSiteFilters({
    searchQuery,
    onSearchChange,
    filters,
    onFiltersChange,
    onClearFilters,
}: WorkSiteFiltersProps) {
    const [isOpen, setIsOpen] = React.useState(false)

    const activeFiltersCount = React.useMemo(() => {
        let count = 0
        if (filters.status?.length) count += filters.status.length
        if (filters.type?.length) count += filters.type.length
        if (filters.manager) count += 1
        if (filters.location) count += 1
        if (filters.dateRange?.start || filters.dateRange?.end) count += 1
        return count
    }, [filters])

    const handleStatusChange = (status: string, checked: boolean) => {
        const currentStatus = filters.status || []
        const newStatus = checked
            ? [...currentStatus, status]
            : currentStatus.filter((s) => s !== status)

        onFiltersChange({
            ...filters,
            status: newStatus.length > 0 ? newStatus : undefined,
        })
    }

    const handleTypeChange = (type: string, checked: boolean) => {
        const currentType = filters.type || []
        const newType = checked
            ? [...currentType, type]
            : currentType.filter((t) => t !== type)

        onFiltersChange({
            ...filters,
            type: newType.length > 0 ? newType : undefined,
        })
    }

    return (
        <div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
            {/* Search */}
            <div className="relative flex-1 max-w-md">
                <IconSearch className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
                <Input
                    placeholder="ค้นหาชื่อไซต์งาน, สถานที่, ผู้จัดการ..."
                    value={searchQuery}
                    onChange={(e) => onSearchChange(e.target.value)}
                    className="pl-10"
                />
            </div>

            {/* Filter Button and Active Filters */}
            <div className="flex items-center gap-2">
                {/* Active Filters Count */}
                {activeFiltersCount > 0 && (
                    <div className="flex items-center gap-2">
                        <Badge variant="secondary">
                            {activeFiltersCount} ตัวกรอง
                        </Badge>
                        <Button
                            variant="ghost"
                            size="sm"
                            onClick={onClearFilters}
                            className="h-8 px-2"
                        >
                            <IconX className="h-3 w-3" />
                            ล้าง
                        </Button>
                    </div>
                )}

                {/* Filter Sheet */}
                <Sheet open={isOpen} onOpenChange={setIsOpen}>
                    <SheetTrigger asChild>
                        <Button variant="outline" size="sm">
                            <IconFilter className="h-4 w-4" />
                            ตัวกรอง
                        </Button>
                    </SheetTrigger>
                    <SheetContent>
                        <SheetHeader>
                            <SheetTitle>ตัวกรองไซต์งาน</SheetTitle>
                            <SheetDescription>
                                เลือกเงื่อนไขในการกรองข้อมูลไซต์งาน
                            </SheetDescription>
                        </SheetHeader>

                        <div className="mt-6 space-y-6">
                            {/* Status Filter */}
                            <div className="space-y-3">
                                <Label className="text-sm font-medium">สถานะ</Label>
                                <div className="space-y-2">
                                    {statusOptions.map((option) => (
                                        <div key={option.value} className="flex items-center space-x-2">
                                            <Checkbox
                                                id={`status-${option.value}`}
                                                checked={filters.status?.includes(option.value) || false}
                                                onCheckedChange={(checked) =>
                                                    handleStatusChange(option.value, checked as boolean)
                                                }
                                            />
                                            <Label
                                                htmlFor={`status-${option.value}`}
                                                className="text-sm font-normal"
                                            >
                                                {option.label}
                                            </Label>
                                        </div>
                                    ))}
                                </div>
                            </div>

                            {/* Type Filter */}
                            <div className="space-y-3">
                                <Label className="text-sm font-medium">ประเภท</Label>
                                <div className="space-y-2">
                                    {typeOptions.map((option) => (
                                        <div key={option.value} className="flex items-center space-x-2">
                                            <Checkbox
                                                id={`type-${option.value}`}
                                                checked={filters.type?.includes(option.value) || false}
                                                onCheckedChange={(checked) =>
                                                    handleTypeChange(option.value, checked as boolean)
                                                }
                                            />
                                            <Label
                                                htmlFor={`type-${option.value}`}
                                                className="text-sm font-normal"
                                            >
                                                {option.label}
                                            </Label>
                                        </div>
                                    ))}
                                </div>
                            </div>

                            {/* Manager Filter */}
                            <div className="space-y-3">
                                <Label htmlFor="manager" className="text-sm font-medium">
                                    ผู้จัดการ
                                </Label>
                                <Input
                                    id="manager"
                                    placeholder="ชื่อผู้จัดการ"
                                    value={filters.manager || ""}
                                    onChange={(e) =>
                                        onFiltersChange({
                                            ...filters,
                                            manager: e.target.value || undefined,
                                        })
                                    }
                                />
                            </div>

                            {/* Location Filter */}
                            <div className="space-y-3">
                                <Label htmlFor="location" className="text-sm font-medium">
                                    สถานที่
                                </Label>
                                <Input
                                    id="location"
                                    placeholder="จังหวัด/เมือง"
                                    value={filters.location || ""}
                                    onChange={(e) =>
                                        onFiltersChange({
                                            ...filters,
                                            location: e.target.value || undefined,
                                        })
                                    }
                                />
                            </div>

                            {/* Date Range Filter */}
                            <div className="space-y-3">
                                <Label className="text-sm font-medium">ช่วงวันที่</Label>
                                <div className="grid grid-cols-2 gap-2">
                                    <div>
                                        <Label htmlFor="start-date" className="text-xs text-muted-foreground">
                                            วันที่เริ่ม
                                        </Label>
                                        <Input
                                            id="start-date"
                                            type="date"
                                            value={filters.dateRange?.start || ""}
                                            onChange={(e) =>
                                                onFiltersChange({
                                                    ...filters,
                                                    dateRange: {
                                                        ...filters.dateRange,
                                                        start: e.target.value,
                                                        end: filters.dateRange?.end || "",
                                                    },
                                                })
                                            }
                                        />
                                    </div>
                                    <div>
                                        <Label htmlFor="end-date" className="text-xs text-muted-foreground">
                                            วันที่สิ้นสุด
                                        </Label>
                                        <Input
                                            id="end-date"
                                            type="date"
                                            value={filters.dateRange?.end || ""}
                                            onChange={(e) =>
                                                onFiltersChange({
                                                    ...filters,
                                                    dateRange: {
                                                        start: filters.dateRange?.start || "",
                                                        end: e.target.value,
                                                    },
                                                })
                                            }
                                        />
                                    </div>
                                </div>
                            </div>

                            {/* Clear Filters */}
                            <Button
                                variant="outline"
                                onClick={onClearFilters}
                                className="w-full"
                            >
                                ล้างตัวกรองทั้งหมด
                            </Button>
                        </div>
                    </SheetContent>
                </Sheet>
            </div>
        </div>
    )
}
