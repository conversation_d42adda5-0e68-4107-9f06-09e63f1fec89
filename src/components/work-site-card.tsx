"use client"

import * as React from "react"
import {
  IconBuilding,
  IconCalendar,
  IconEdit,
  IconHeart,
  IconHeartFilled,
  IconMapPin,
  IconPhone,
  IconUsers,
  IconMail,
  IconProgress,
  IconCurrencyBaht,
} from "@tabler/icons-react"

import { WorkSite } from "@/types/work-site"
import { useFavorites } from "@/hooks/use-favorites"
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { Progress } from "@/components/ui/progress"
import { cn } from "@/lib/utils"

interface WorkSiteCardProps {
  workSite: WorkSite
}

const statusColors = {
  Active: "bg-green-500",
  Inactive: "bg-gray-500",
  Maintenance: "bg-yellow-500",
  Completed: "bg-blue-500",
}

const typeIcons = {
  Construction: IconBuilding,
  Manufacturing: IconBuilding,
  Office: IconBuilding,
  Warehouse: IconBuilding,
  Retail: IconBuilding,
}

export function WorkSiteCard({ workSite }: WorkSiteCardProps) {
  const TypeIcon = typeIcons[workSite.type]
  const { isFavorite, toggleFavorite } = useFavorites()

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('th-TH', {
      style: 'currency',
      currency: 'THB',
      minimumFractionDigits: 0,
    }).format(amount)
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('th-TH', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    })
  }

  const handleEdit = () => {
    window.location.href = `/work-sites/${workSite.id}/edit`
  }

  return (
    <Card className="group relative overflow-hidden transition-all hover:shadow-lg">
      {/* Favorite Button */}
      <Button
        variant="ghost"
        size="sm"
        className="absolute right-2 top-2 z-10 h-8 w-8 p-0 opacity-0 transition-opacity group-hover:opacity-100"
        onClick={() => toggleFavorite(workSite.id)}
      >
        {isFavorite(workSite.id) ? (
          <IconHeartFilled className="h-4 w-4 text-red-500" />
        ) : (
          <IconHeart className="h-4 w-4" />
        )}
      </Button>

      {/* Edit Button */}
      <Button
        variant="ghost"
        size="sm"
        className="absolute right-12 top-2 z-10 h-8 w-8 p-0 opacity-0 transition-opacity group-hover:opacity-100"
        onClick={handleEdit}
      >
        <IconEdit className="h-4 w-4" />
      </Button>

      <CardHeader className="pb-3">
        <div className="flex items-start justify-between">
          <div className="flex items-center gap-2">
            <TypeIcon className="h-5 w-5 text-muted-foreground" />
            <div>
              <CardTitle className="text-lg">{workSite.name}</CardTitle>
              <CardDescription className="flex items-center gap-1 mt-1">
                <IconMapPin className="h-3 w-3" />
                {workSite.location}
              </CardDescription>
            </div>
          </div>
        </div>
        
        <div className="flex items-center gap-2 mt-2">
          <Badge
            variant="secondary"
            className={cn("text-white", statusColors[workSite.status])}
          >
            {workSite.status}
          </Badge>
          <Badge variant="outline">{workSite.type}</Badge>
        </div>
      </CardHeader>

      <CardContent className="space-y-4">
        {/* Progress */}
        <div className="space-y-2">
          <div className="flex items-center justify-between text-sm">
            <span className="flex items-center gap-1">
              <IconProgress className="h-3 w-3" />
              ความคืบหน้า
            </span>
            <span className="font-medium">{workSite.progress}%</span>
          </div>
          <Progress value={workSite.progress} className="h-2" />
        </div>

        {/* Manager and Employees */}
        <div className="grid grid-cols-2 gap-4 text-sm">
          <div>
            <p className="text-muted-foreground">ผู้จัดการ</p>
            <p className="font-medium">{workSite.manager}</p>
          </div>
          <div>
            <p className="text-muted-foreground flex items-center gap-1">
              <IconUsers className="h-3 w-3" />
              พนักงาน
            </p>
            <p className="font-medium">{workSite.employees} คน</p>
          </div>
        </div>

        {/* Budget */}
        <div className="text-sm">
          <p className="text-muted-foreground flex items-center gap-1">
            <IconCurrencyBaht className="h-3 w-3" />
            งบประมาณ
          </p>
          <p className="font-medium text-lg">{formatCurrency(workSite.budget)}</p>
        </div>

        {/* Dates */}
        <div className="grid grid-cols-2 gap-4 text-sm">
          <div>
            <p className="text-muted-foreground flex items-center gap-1">
              <IconCalendar className="h-3 w-3" />
              เริ่มต้น
            </p>
            <p className="font-medium">{formatDate(workSite.startDate)}</p>
          </div>
          {workSite.endDate && (
            <div>
              <p className="text-muted-foreground">สิ้นสุด</p>
              <p className="font-medium">{formatDate(workSite.endDate)}</p>
            </div>
          )}
        </div>

        {/* Description */}
        <div className="text-sm">
          <p className="text-muted-foreground line-clamp-2">{workSite.description}</p>
        </div>

        {/* Tags */}
        {workSite.tags.length > 0 && (
          <div className="flex flex-wrap gap-1">
            {workSite.tags.map((tag, index) => (
              <Badge key={index} variant="outline" className="text-xs">
                {tag}
              </Badge>
            ))}
          </div>
        )}
      </CardContent>

      <CardFooter className="pt-0">
        <div className="flex w-full items-center justify-between text-xs text-muted-foreground">
          <div className="flex items-center gap-1">
            <IconPhone className="h-3 w-3" />
            {workSite.contact.phone}
          </div>
          <div className="flex items-center gap-1">
            <IconMail className="h-3 w-3" />
            {workSite.contact.email}
          </div>
        </div>
      </CardFooter>
    </Card>
  )
}
