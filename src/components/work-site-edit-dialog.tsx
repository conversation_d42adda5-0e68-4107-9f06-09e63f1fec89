"use client"

import * as React from "react"
import { WorkSite } from "@/types/work-site"
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  <PERSON><PERSON><PERSON>ooter,
  <PERSON><PERSON>Header,
  DialogTitle,
} from "@/components/ui/dialog"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"

interface WorkSiteEditDialogProps {
  workSite: WorkSite | null
  open: boolean
  onOpenChange: (open: boolean) => void
  onSave: (workSite: WorkSite) => void
}

export function WorkSiteEditDialog({
  workSite,
  open,
  onOpenChange,
  onSave,
}: WorkSiteEditDialogProps) {
  const [formData, setFormData] = React.useState<Partial<WorkSite>>({})

  React.useEffect(() => {
    if (workSite) {
      setFormData(workSite)
    }
  }, [workSite])

  const handleSave = () => {
    if (formData && workSite) {
      onSave({ ...workSite, ...formData } as WorkSite)
      onOpenChange(false)
    }
  }

  const updateFormData = (field: keyof WorkSite, value: any) => {
    setFormData((prev) => ({ ...prev, [field]: value }))
  }

  const updateNestedFormData = (
    parentField: keyof WorkSite,
    field: string,
    value: any
  ) => {
    setFormData((prev) => ({
      ...prev,
      [parentField]: {
        ...(prev[parentField] as any),
        [field]: value,
      },
    }))
  }

  if (!workSite) return null

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>แก้ไขข้อมูลไซต์งาน</DialogTitle>
          <DialogDescription>
            แก้ไขข้อมูลของไซต์งาน {workSite.name}
          </DialogDescription>
        </DialogHeader>

        <div className="grid gap-4 py-4">
          {/* Basic Information */}
          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="name">ชื่อไซต์งาน</Label>
              <Input
                id="name"
                value={formData.name || ""}
                onChange={(e) => updateFormData("name", e.target.value)}
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="location">สถานที่</Label>
              <Input
                id="location"
                value={formData.location || ""}
                onChange={(e) => updateFormData("location", e.target.value)}
              />
            </div>
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="status">สถานะ</Label>
              <Select
                value={formData.status || ""}
                onValueChange={(value) => updateFormData("status", value)}
              >
                <SelectTrigger>
                  <SelectValue placeholder="เลือกสถานะ" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="Active">ใช้งาน</SelectItem>
                  <SelectItem value="Inactive">ไม่ใช้งาน</SelectItem>
                  <SelectItem value="Maintenance">ซ่อมบำรุง</SelectItem>
                  <SelectItem value="Completed">เสร็จสิ้น</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div className="space-y-2">
              <Label htmlFor="type">ประเภท</Label>
              <Select
                value={formData.type || ""}
                onValueChange={(value) => updateFormData("type", value)}
              >
                <SelectTrigger>
                  <SelectValue placeholder="เลือกประเภท" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="Construction">ก่อสร้าง</SelectItem>
                  <SelectItem value="Manufacturing">โรงงาน</SelectItem>
                  <SelectItem value="Office">สำนักงาน</SelectItem>
                  <SelectItem value="Warehouse">คลังสินค้า</SelectItem>
                  <SelectItem value="Retail">ร้านค้า</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="manager">ผู้จัดการ</Label>
              <Input
                id="manager"
                value={formData.manager || ""}
                onChange={(e) => updateFormData("manager", e.target.value)}
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="employees">จำนวนพนักงาน</Label>
              <Input
                id="employees"
                type="number"
                value={formData.employees || ""}
                onChange={(e) => updateFormData("employees", parseInt(e.target.value) || 0)}
              />
            </div>
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="budget">งบประมาณ</Label>
              <Input
                id="budget"
                type="number"
                value={formData.budget || ""}
                onChange={(e) => updateFormData("budget", parseInt(e.target.value) || 0)}
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="progress">ความคืบหน้า (%)</Label>
              <Input
                id="progress"
                type="number"
                min="0"
                max="100"
                value={formData.progress || ""}
                onChange={(e) => updateFormData("progress", parseInt(e.target.value) || 0)}
              />
            </div>
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="startDate">วันที่เริ่มต้น</Label>
              <Input
                id="startDate"
                type="date"
                value={formData.startDate || ""}
                onChange={(e) => updateFormData("startDate", e.target.value)}
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="endDate">วันที่สิ้นสุด</Label>
              <Input
                id="endDate"
                type="date"
                value={formData.endDate || ""}
                onChange={(e) => updateFormData("endDate", e.target.value)}
              />
            </div>
          </div>

          {/* Contact Information */}
          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="phone">เบอร์โทรศัพท์</Label>
              <Input
                id="phone"
                value={formData.contact?.phone || ""}
                onChange={(e) => updateNestedFormData("contact", "phone", e.target.value)}
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="email">อีเมล</Label>
              <Input
                id="email"
                type="email"
                value={formData.contact?.email || ""}
                onChange={(e) => updateNestedFormData("contact", "email", e.target.value)}
              />
            </div>
          </div>

          {/* Address */}
          <div className="space-y-2">
            <Label htmlFor="street">ที่อยู่</Label>
            <Input
              id="street"
              value={formData.address?.street || ""}
              onChange={(e) => updateNestedFormData("address", "street", e.target.value)}
            />
          </div>

          <div className="grid grid-cols-3 gap-4">
            <div className="space-y-2">
              <Label htmlFor="city">เมือง/อำเภอ</Label>
              <Input
                id="city"
                value={formData.address?.city || ""}
                onChange={(e) => updateNestedFormData("address", "city", e.target.value)}
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="province">จังหวัด</Label>
              <Input
                id="province"
                value={formData.address?.province || ""}
                onChange={(e) => updateNestedFormData("address", "province", e.target.value)}
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="postalCode">รหัสไปรษณีย์</Label>
              <Input
                id="postalCode"
                value={formData.address?.postalCode || ""}
                onChange={(e) => updateNestedFormData("address", "postalCode", e.target.value)}
              />
            </div>
          </div>

          {/* Description */}
          <div className="space-y-2">
            <Label htmlFor="description">รายละเอียด</Label>
            <Textarea
              id="description"
              value={formData.description || ""}
              onChange={(e) => updateFormData("description", e.target.value)}
              rows={3}
            />
          </div>
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            ยกเลิก
          </Button>
          <Button onClick={handleSave}>บันทึก</Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
